% !TEX program = xelatex
\input{preamble.tex}

\begin{document}

% --- Cover Page ---
% All cover page elements are defined in preamble.tex and cover.tex
\input{cover.tex}
\thispagestyle{empty} % No header/footer on cover
\cleardoublepage

% --- Table of Contents ---
\pagenumbering{roman} % Roman numerals for ToC
\tableofcontents
\cleardoublepage

% --- Main Content ---·
\pagenumbering{arabic} % Arabic numerals for main content
\setcounter{page}{1}
\pagestyle{fancy} % Use the fancy style defined in preamble

\input{sections/summary.tex}

% --- Signature Block ---
\vfill % Push the signature block to the bottom of the page

\noindent
\begin{minipage}[t]{0.48\textwidth}
    \begin{tabular}{ll}
        课题组负责人签字: & \underline{\hspace{3cm}} \\[1.5cm]
        日期: & \underline{\hspace{3cm}}
    \end{tabular}
\end{minipage}
\hfill
\begin{minipage}[t]{0.48\textwidth}
    \begin{tabular}{ll}
        实习生签字: & \underline{\hspace{3cm}} \\[1.5cm]
        日期: & \underline{\hspace{3cm}}
    \end{tabular}
\end{minipage}

\end{document} 