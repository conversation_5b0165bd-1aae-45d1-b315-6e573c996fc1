% !TEX root = main.tex

% --- Document Class ---
\documentclass[UTF8, 12pt, a4paper]{ctexart}

% --- Packages ---
\usepackage{graphicx} % For including images
\usepackage[left=2.5cm, right=2.5cm, top=2.5cm, bottom=2.5cm]{geometry} % Page margins
\usepackage{amsmath, amssymb, amsfonts} % Math packages
\usepackage{fontspec} % Use fontspec for font selection in XeLaTeX
\setmainfont{Times New Roman} % A classic Roman serif font
\setCJKmainfont{Songti SC} % For macOS: 宋体-简
\setCJKsansfont{PingFang SC} % For macOS: 苹方-简
\setCJKmonofont{PingFang SC} % For mono Chinese font
\usepackage{fancyhdr} % For headers and footers
\usepackage{hyperref} % For hyperlinks, like in the table of contents
\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=magenta,
    urlcolor=blue,
    citecolor=blue,
}
\usepackage{lipsum} % For generating dummy text, remove in final version
\usepackage{algorithm} % For algorithm environment
\usepackage{algpseudocode} % For algorithmic environment


% --- Document Information ---
\newcommand{\reportTitle}{访问工作报告}
\newcommand{\reportSubtitle}{（七月份）}
\newcommand{\studentName}{黄天野}
\newcommand{\studentUniversity}{香港理工大学}
\newcommand{\internshipOrg}{深圳医学科学院}
\newcommand{\internshipGroup}{胡名旭课题组}
\newcommand{\reportDate}{\today}


% --- Header and Footer Setup ---
\setlength{\headheight}{14.5pt} % Fix header height warning
\pagestyle{fancy}
\fancyhf{} % Clear all header and footer fields
\fancyhead[C]{深圳医学科学院访问报告} % Center header
\fancyfoot[C]{\thepage} % Center footer with page number
\renewcommand{\headrulewidth}{0.4pt}
\renewcommand{\footrulewidth}{0.4pt}