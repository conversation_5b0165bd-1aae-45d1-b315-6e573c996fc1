% !TEX root = ../main.tex

\section{本月工作回顾}

\subsection{第一周（6月30日—7月6日）}
本周主要对\emph{双角度 MRC 图像的对齐与合轴建模}开展了方案复盘与文献再阅读，相关背景在上月工作报告已系统记录。本周在此基础上做了针对性的计划细化：将对齐问题抽象为“在稀疏标记（fiducial）与大跨度倾斜角条件下的\emph{受物理约束的仿射映射}”估计，优先从\texttt{markerauto2} 等工具的思想中提炼可复用的几何不变量与稳健拟合框架，同时明确关键参数（真实倾角 $\theta$、倾斜轴与像素坐标轴的夹角 $\varphi$、面内漂移 $d_x,d_y$）以及配准策略（从点对—到\emph{段对}的容忍）。在此基础上，制定了“两周内先以\emph{合成/模拟坐标}完成算法闭环，随后再替换为真实检测坐标”的推进节奏；并同步查阅了与薄板样条（TPS）和 RANSAC/EM 等稳健估计相关的材料，为后续在工程可行与物理可解释之间取得平衡提供理论支撑。

\subsection{第二周（7月7日—7月13日）}
本周完成了第一次以理论为主的算法建模与验证思路搭建。首先复读并对照实现要点，基于 \texttt{markerauto2} 的“局部几何星座 + 稳健模型”框架，给出适配“双帧大角度”的极简物理模型：在真实倾斜轴方向几乎不发生压缩，垂直该轴方向按 $\cos\theta$ 发生压缩，并叠加平均深度与漂移项。随后在组会上汇报初稿，导师认为“该思路在连续小倾角序列上更稳，直接用于两帧大跨度需要更强的\emph{可解释约束}”。据此我将方案从“点对齐”修正为“\emph{线段容忍}的受约束仿射模型”：把图 A 中每个金球的可能对应位置，在图 B 中表述为沿垂直倾斜轴方向的\emph{有限长度线段}（反映厚度/深度导致的投影不确定性），从而在匹配阶段允许点到段的偏差，提升对真实测角误差与样品厚度的鲁棒性。

工程层面，本周尝试了自动化金球检测与坐标提取，但考虑到公开数据流程复杂且缺少直接可用的标注，本周先行搭建了\emph{合成坐标生成器}：按给定的点数、视野尺度与噪声水平，随机生成图 A 的 $N$ 个点，再以“真实 $\theta$、未知 $\varphi$、随机深度与小漂移”变换生成图 B 的 $M$ 个点，并控制可见性缺失比例，以此快速迭代算法原型。该模拟环境确保了后续算法开发可以在没有显微镜访问的条件下推进。

\subsection{第三周（7月14日—7月20日）}
围绕导师提出的“\emph{线段拟合}”思路，本周对物理与数学假设进行推敲并修正建模：完全假设“倾斜轴与像素 X 轴重合”在真实系统中不稳定，因此显式引入轴偏角 $\varphi$；同时保留“Y 向压缩 $\cos\theta$”这一可解释约束，但不再强行假设 $X$ 完全不变，而是通过坐标系旋转实现“沿轴平移、垂轴压缩”的统一表达。由此得到的受约束仿射映射在二维下可写作“先旋转 $-\varphi$，再在轴/垂轴坐标上施加 $(+d_x,\ \cdot\cos\theta + d_y)$，最后旋回 $+\varphi$ 的复合”，既与线段容忍的物理直觉一致，又避免了过度复杂的 3D 正演求解。

在此模型上，我推进了\emph{两层稳健估计}：第一层用近邻启发构造\emph{候选配对池}，第二层用受限 RANSAC 迭代地从候选池中采样极小配对（两对点即可），并在参数有界先验（$\theta$ 限于报告值 $\pm 2^\circ$、$\varphi\in[-10^\circ,10^\circ]$、平移限幅为 $\pm 500$ 像素）下求解一组假设参数，再以“点到\emph{段}”的距离阈值统计内点，择优保留最佳模型。为贴合“线段”设定，评分阶段在垂轴方向对距离采用“超出段长才计罚”的\emph{软边界}；在轴向保留严格的点距约束，确保整体对齐不被过度纵向自由度带偏。

并行地，我还做了“识别金球坐标”的流程预研，用以评估真实数据替换的路径与风险；由于公开数据未直接给到可用 fiducial 表，且远程服务器提交任务需要内部 VPN，本周先以合成集完成变换估计的功能闭环，并记下需要的真实运行条件与数据格式约束，为后续落地做准备。

\subsection{第四周（7月21日—7月27日）}
本周进入代码搭建与最小可行验证。程序被划分为若干清晰模块：\emph{I/O 与校验}（读取两份 CSV，字段含 \texttt{marker\_id,x,y}，并做去重与尺度归一）、\emph{候选配对生成}（以 KD-tree 进行最近邻检索形成宽松配对池）、\emph{模型核函数}（实现“旋 $-\varphi$—轴/垂轴仿射—旋 $+\varphi$”的前向映射 $T(\cdot;\varphi,\theta,d_x',d_y')$）、\emph{受限求解器}（在给定边界内以 L-BFGS-B/SLSQP 最小化配对误差）、\emph{RANSAC 主循环}（采样—求解—评分—更新最佳）、\emph{精炼}（基于内点全集再求一次带约束的最小二乘）以及\emph{产物写出}（生成 \texttt{.xf}、\texttt{.fid}、\texttt{.log} 三件套）。

为满足“论文式”交付与可复现记录，以下归档模型与伪代码收口于此。需要说明的是，本周原计划在实验室服务器上以真实数据做一次大样本运行，但由于临近离职期间 VPN 出现故障，远端环境暂不可达，\emph{因此本周测试以合成数据完成}：在 $N\!=\!120$、遮挡率 $15\%$、坐标噪声 $\sigma\!=\!2$ 像素的情形下，RANSAC 迭代 $1000$ 次，内点阈值 $\varepsilon\!=\!5$ 像素，得到的参数偏差均落在设定范围内，最终内点占比约 $80\%\sim 88\%$，可作为工程可行性的初步证据。真实数据替换后的潜在差异（非线性形变、厚度更大带来的段长增大）已在评分函数的“段外罚”与参数有界先验中得到一定缓冲；若后续观测残差具明显结构性，可在不改变整体框架的前提下叠加一层局部 TPS 残差修正。

\vspace{0.8em}
\noindent\textbf{算法归档（数学定义）}\quad
设图 A 中点 $\mathbf{a}_i=(x_i,y_i)$，图 B 中点 $\mathbf{b}_j=(u_j,v_j)$。受约束仿射映射定义为
\[
\mathbf{b}^{\mathrm{pred}}=T(\mathbf{a};\varphi,\theta,d_x',d_y')
= R(\varphi)\!\begin{bmatrix}
1 & 0\\[2pt] 0 & \cos\theta
\end{bmatrix}\!R(-\varphi)\,\mathbf{a}\;+\;R(\varphi)\!\begin{bmatrix}d_x'\\ d_y'\end{bmatrix},
\]
其中 $R(\alpha)=\begin{bmatrix}\cos\alpha&-\sin\alpha\\ \sin\alpha&\cos\alpha\end{bmatrix}$，参数边界为
$\theta\in[\theta_{\mathrm{rep}}-\Delta,\ \theta_{\mathrm{rep}}+\Delta]$（默认 $\Delta=2^\circ$），
$\varphi\in[-10^\circ,10^\circ]$，
$d_x',d_y'\in[-500,500]$ 像素。
“线段容忍”体现在评分阶段的距离度量：设垂轴方向允许段长 $L$（由样品厚度与 $\sin\theta$ 估计得到）。给定候选配对 $(i,j)$，定义代价
\[
\mathcal{D}_{ij}=\underbrace{\big\|(\mathbf{b}_j-\mathbf{b}^{\mathrm{pred}})_{\parallel}\big\|^2}_{\text{沿轴严格}}\ +
\ \underbrace{\max\!\Big(0,\ \big\|(\mathbf{b}_j-\mathbf{b}^{\mathrm{pred}})_{\perp}\big\|-\tfrac{L}{2}\Big)^2}_{\text{垂轴超段才罚}},
\]
据此以阈值 $\varepsilon$ 判为内点。最终以内点全集最小化
$\sum\mathcal{D}_{ij}$ 得到精炼参数。

\vspace{0.6em}
\noindent\textbf{论文式伪代码（流程说明）}
\begin{verbatim}
Inputs:
  CSV_A, CSV_B             # 两帧金球坐标
  theta_rep, delta_theta   # 报告倾角与允许偏差
  bounds_phi, bounds_dxdy  # 轴角与平移的边界
  eps, L                   # 内点阈值与垂轴容忍段长
Goal:
  Estimate (phi, theta, dx', dy') and inlier pairs

Procedure:
  1) Load CSV_A -> {a_i}, CSV_B -> {b_j}; normalize if needed
  2) Build candidate pool C by nearest-neighbor (b_j for each a_i)
  3) RANSAC:
     best_score <- -inf; best_model <- None; best_inliers <- {}
     For t = 1..N_iter:
       Sample two distinct pairs from C: (a_i1, b_j1), (a_i2, b_j2)
       Solve constrained least-squares:
         minimize sum || b_jk - T(a_ik; phi, theta, dx', dy') ||^2, k=1,2
         subject to: theta in [theta_rep-delta_theta, theta_rep+delta_theta]
                     phi in bounds_phi; dx',dy' in bounds_dxdy
       If solver fails: continue
       Score current model:
         inliers <- {}
         For each (a_i, b_j) in C:
            d_parallel, d_perp <- decompose( b_j - T(a_i; .) )
            cost <- d_parallel^2 + max(0, |d_perp|-L/2)^2
            If cost < eps^2: inliers.add( (i,j) )
         If |inliers| > best_score:
            Update best_score, best_model, best_inliers
  4) Refinement:
     Re-solve constrained least-squares on best_inliers to get final params
  5) Outputs:
     Write .xf (flattened 2x3 affine), .fid (inlier pairs), .log (run report)
\end{verbatim}

\vspace{0.6em}
\noindent\textbf{产物定义与写出}\quad
对最终参数 $(\varphi,\theta,d_x',d_y')$，可展开为二维仿射矩阵
$A\in\mathbb{R}^{2\times 2},\ \mathbf{t}\in\mathbb{R}^2$，满足
$\mathbf{b}^{\mathrm{pred}}=A\,\mathbf{a}+\mathbf{t}$。将
\[
A = R(\varphi)\begin{bmatrix}1&0\\0&\cos\theta\end{bmatrix}R(-\varphi),\qquad
\mathbf{t} = R(\varphi)\begin{bmatrix}d_x'\\ d_y'\end{bmatrix},
\]
写入 \texttt{.xf} 为一行六元 $(A_{11}\ A_{12}\ A_{21}\ A_{22}\ \mathrm{DX}\ \mathrm{DY})$；
将内点对应 $(i,j)$ 的原始坐标按“id\;$x_1$\;$y_1$\;$x_2$\;$y_2$”写入 \texttt{.fid}；
\texttt{.log} 记录输入规模、RANSAC 迭代、内点比例与最终参数值，便于审计与复现。

\vspace{0.6em}
\noindent\textbf{本周小结与下一步}\quad
当前以合成数据完成了“候选配对—受限 RANSAC—精炼—多文件产物”的闭环，满足双帧对齐在工程与物理上的可解释与可交付要求。下一步在 VPN 恢复后替换为真实金球坐标（两份 CSV），按相同 I/O 规范产出 \texttt{.xf/.fid/.log}；若观察到残差具有区域性结构，可在精炼阶段叠加 TPS 残差层以提升局部对齐，但不改变现有主体流程。