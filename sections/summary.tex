% !TEX root = ../main.tex

\section{本月工作回顾}

\subsection{第一周 (6月30日-7月6日)}
相关内容已在上月工作报告中提到提到，并计划了后续任务为接下来将重点推进双角度 mrc 图像的对齐与合轴建模工作，包括基于现有数据设计数学模型，明确关键参数与配准策略，并结合实际样本开展初步仿真测试，逐步完善密
度积分影像的三维重建流程。同时，将继续查阅并学习相关文献，以不断优化和完善建模与合轴方法，为后续项目的深入开展提供理论和方法基础。

\subsection{第二周 (7月7日-7月13)}
第二周（7月7日–7月13日）工作回顾

本周的核心工作是针对“两帧大倾角 mrc 图像的金颗粒自动配准”建立初版理论方案，并通过文献学习、算法设计和数学推导形成可行模型。由于此前对该方向缺乏直接经验，我在师兄的推荐下，深入阅读了 Markerauto2 的论文与技术报告，查阅了计算机视觉中的立体匹配（Stereo）原理，并把过往工程建模与 CAD 中的集合变换经验一起纳入思考，形成具体的数学建模路径。

阅读阶段我重点把三类思想“对齐”起来。第一，Markerauto2 的“模板检测→结构化对应→全局优化”框架，本质上用少量高置信的基准点先立一个几何参照，再“把其余点吸到参照上”。第二，CAD 中的集合到集合的相似/仿射配准（典型如 Procrustes/Umeyama），强调以“小而稳”的基准子集估计 A,t 后再推广到整集合，和前者在方法学上同构。第三，“星座模型”的要义是“部件 + 几何关系”，即用点集内部的相对结构当作先验，不依赖逐点一对一的脆弱匹配。三者共同给了我一个简洁原则：先用“模板定义基准”，再在结构约束下“布点定位”。

据此，我设计了两帧大倾角的首版配准流程。首先对两帧做金球亚像素检测，选出一组空间分布良好的“基准子集”，用四点仿射不变量在 RANSAC 中估计 A\in\mathbb R^{2\times2},\,t\in\mathbb R^{2}，先把集合到集合的大体位姿对上；随后将坐标旋到以倾转轴为 a 轴、垂轴为 p 轴的系里，利用大倾角下的正交投影近似，写成
p’ \;=\; p\cos\beta \;+\; z\sin\beta \;+\; t_p,
在已得到的 A,t 基础上由线性回归粗估 \beta，并闭式给出每个金球的初始深度
z \;=\; \frac{p’ - p\cos\beta}{\sin\beta}.
最后以“星座式”结构为权重先验做 IRLS 微调，在整体一致性与局部残差之间取得稳健收敛。这里的“集合配准→旋轴→深度解耦”三步，对应 CAD 的“点—面—体”层层约束：先把集合的形对准，再在物理几何上锁轴与角，最后还原体素层面的 z。

为验证几何链条的自洽性，我引入了三维齐次变换与投影的推导作为“对齐证明”。设世界点 X_A=(X,Y,Z,1)^\top 在 0° 帧下的坐标，经绕 Y 轴旋转 \beta 得
X_B \;=\; T(\beta)\,X_A,\quad T(\beta)=
\begin{bmatrix}
\cos\beta & 0 & \sin\beta & 0\\
0&1&0&0\\
-\sin\beta&0&\cos\beta&0\\
0&0&0&1
\end{bmatrix},
再经正交投影 P=\begin{bmatrix}1&0&0&0\\[2pt]0&1&0&0\end{bmatrix} 得到像点 (a’,p’)^\top = P\,X_B + t。把坐标轴重标到 (a,p) 后即可推出上面的 p’–p 关系式，用于反解 \beta 与 z。这套 3D 变换—2D 投影—轴向解耦的推导，也作为实现时对齐正确性的“数学校验”。

7 月 9 日向胡老师汇报时，他肯定了“先基准再定位、以结构约束布点”的路线，但指出两帧大倾角场景下点对稀疏、畸变重，几何法的可辨识度与收敛域会变窄，更适合连续小倾斜。基于此，他提出了一个更“物理极简”的替代思路：在理想设备下，旋转轴沿 Y，两帧 X 坐标近似不变，Y 方向受 \beta 与样品深度 Z 的压缩影响；已知角度与厚度范围即可把配准转化为受限函数拟合，允许用穷举或学习方法在可控边界内直接搜索全局最佳映射。本周到此收束：首版几何方案完成建模与验证思路，替代方案明确约束与搜索框架，二者的实现与对比放到下一周推进。

\subsection{第三周 (7月14日-7月20日)}

\subsection{第四周 (7月21日-7月27日)}
